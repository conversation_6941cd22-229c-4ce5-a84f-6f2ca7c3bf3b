
import {
    Ant<PERSON>esign,
    Enty<PERSON>,
    EvilIcons,
    <PERSON>ather,
    FontAwesome,
    FontAwesome5,
    FontAwesome6,
    Fontisto,
    Foundation,
    Ionicons,
    MaterialCommunityIcons,
    MaterialIcons,
    Octicons,
    SimpleLineIcons,
    Zocial
} from '@expo/vector-icons';
import React from 'react';
import { TextStyle } from 'react-native';

interface IconSymbolProps {
  name: string;
  size: number;
  color: string;
  style?: TextStyle;
}

// Map SF Symbol names to Expo Vector Icons
const iconMap: Record<string, { family: string; name: string }> = {
  // Dashboard & Navigation icons
  'house.fill': { family: 'Ionicons', name: 'home' },
  'chart.bar.fill': { family: 'Ionicons', name: 'stats-chart' },
  'chart.pie.fill': { family: 'Ionicons', name: 'pie-chart' },
  'person.2.fill': { family: 'Ionicons', name: 'people' },
  'person.crop.circle.fill': { family: 'Ionicons', name: 'person-circle' },
  'doc.text.fill': { family: 'Ionicons', name: 'document-text' },
  'person.fill.badge.plus': { family: 'MaterialCommunityIcons', name: 'account-plus' },
  'doc.richtext.fill': { family: 'Ionicons', name: 'document' },
  'arrow.up.right': { family: 'Feather', name: 'arrow-up-right' },
  'arrow.down.right': { family: 'Feather', name: 'arrow-down-right' },
  'exclamationmark.triangle.fill': { family: 'Ionicons', name: 'warning' },
  'chevron.right': { family: 'Ionicons', name: 'chevron-forward' },
  'chevron.left': { family: 'Ionicons', name: 'chevron-back' },
  'bolt.fill': { family: 'Ionicons', name: 'flash' },

  // Form & Input icons
  'person.fill': { family: 'Ionicons', name: 'person' },
  'phone.fill': { family: 'Ionicons', name: 'call' },
  'envelope.fill': { family: 'Ionicons', name: 'mail' },
  'building.2.fill': { family: 'FontAwesome5', name: 'building' },
  'note.text': { family: 'Ionicons', name: 'document-text-outline' },

  // Activity & Status icons
  'person.badge.plus.fill': { family: 'MaterialCommunityIcons', name: 'account-plus' },

  // Additional icons for enhanced UI
  'lock.fill': { family: 'Ionicons', name: 'lock-closed' },
  'eye.fill': { family: 'Ionicons', name: 'eye' },
  'eye.slash.fill': { family: 'Ionicons', name: 'eye-off' },
  'calendar': { family: 'Ionicons', name: 'calendar' },
  'clock.fill': { family: 'Ionicons', name: 'time' },
  'location.fill': { family: 'Ionicons', name: 'location' },
  'bell.fill': { family: 'Ionicons', name: 'notifications' },
  'gear': { family: 'Ionicons', name: 'settings' },
  'info.circle.fill': { family: 'Ionicons', name: 'information-circle' },
  'checkmark.circle.fill': { family: 'Ionicons', name: 'checkmark-circle' },
  'xmark.circle.fill': { family: 'Ionicons', name: 'close-circle' },
  'questionmark.circle.fill': { family: 'Ionicons', name: 'help-circle' },
  'checkmark.seal.fill': { family: 'Ionicons', name: 'checkmark-done-circle' },
  'plus.circle.fill': { family: 'Ionicons', name: 'add-circle' },
  'minus.circle.fill': { family: 'Ionicons', name: 'remove-circle' },
  'pencil': { family: 'Ionicons', name: 'pencil' },
  'trash.fill': { family: 'Ionicons', name: 'trash' },
  'arrow.clockwise': { family: 'Ionicons', name: 'refresh' },
  'arrow.up': { family: 'Ionicons', name: 'arrow-up' },
  'arrow.down': { family: 'Ionicons', name: 'arrow-down' },
  'arrow.left': { family: 'Ionicons', name: 'arrow-back' },
  'arrow.right': { family: 'Ionicons', name: 'arrow-forward' },
  'star.fill': { family: 'Ionicons', name: 'star' },
  'heart.fill': { family: 'Ionicons', name: 'heart' },
  'bookmark.fill': { family: 'Ionicons', name: 'bookmark' },
  'share': { family: 'Ionicons', name: 'share' },
  'search': { family: 'Ionicons', name: 'search' },
  'filter': { family: 'Ionicons', name: 'filter' },
  'sort': { family: 'Ionicons', name: 'funnel' },
  'camera.fill': { family: 'Ionicons', name: 'camera' },
  'photo.fill': { family: 'Ionicons', name: 'image' },
  'mic.fill': { family: 'Ionicons', name: 'mic' },
  'speaker.fill': { family: 'Ionicons', name: 'volume-high' },
  'speaker.slash.fill': { family: 'Ionicons', name: 'volume-mute' },
  'wifi': { family: 'Ionicons', name: 'wifi' },
  'wifi.slash': { family: 'Ionicons', name: 'wifi-outline' },
  'cloud.fill': { family: 'Ionicons', name: 'cloud' },
  'cloud.upload.fill': { family: 'Ionicons', name: 'cloud-upload' },
  'cloud.download.fill': { family: 'Ionicons', name: 'cloud-download' },
  'plus': { family: 'Ionicons', name: 'add' },
  'xmark': { family: 'Ionicons', name: 'close' },
  'magnifyingglass': { family: 'Ionicons', name: 'search' },
  'mappin.and.ellipse': { family: 'Ionicons', name: 'location' },
  'arrow.counterclockwise': { family: 'Ionicons', name: 'arrow-undo' },
  'arrow.up.doc.fill': { family: 'Ionicons', name: 'cloud-upload' },
  'checkmark': { family: 'Ionicons', name: 'checkmark' },
  'useAuth': { family: 'Ionicons', name: 'person' },

  // Logout & Authentication icons
  'arrow.right.square.fill': { family: 'Ionicons', name: 'log-out' },
  'arrow.left.square.fill': { family: 'Ionicons', name: 'log-in' },
  'power': { family: 'Ionicons', name: 'power' },
  'exit': { family: 'Ionicons', name: 'exit' },

  // Election & Voting specific icons
  'vote': { family: 'MaterialCommunityIcons', name: 'vote' },
  'ballot': { family: 'MaterialCommunityIcons', name: 'ballot' },
  'account-group': { family: 'MaterialCommunityIcons', name: 'account-group' },
  'podium': { family: 'MaterialCommunityIcons', name: 'podium' },
  'flag': { family: 'Ionicons', name: 'flag' },
  'megaphone': { family: 'MaterialCommunityIcons', name: 'bullhorn' },
  'clipboard.fill': { family: 'Ionicons', name: 'clipboard' },
  'checkbox.fill': { family: 'Ionicons', name: 'checkbox' },
  'map.fill': { family: 'Ionicons', name: 'map' },
  'pin.fill': { family: 'Ionicons', name: 'pin' },

  // Additional icons for screens
  'person.crop.circle.fill': { family: 'Ionicons', name: 'person-circle' },
  'xmark.circle.fill': { family: 'Ionicons', name: 'close-circle' },
  'person.2.circle': { family: 'Ionicons', name: 'people-circle' },
  'arrow.triangle.branch': { family: 'Ionicons', name: 'git-branch' },
};

/**
 * A component that renders icons from @expo/vector-icons based on SF Symbol names
 */
export function IconSymbol({ name, size, color, style }: IconSymbolProps) {
  // Get the mapped icon or use a default
  const iconInfo = iconMap[name] || { family: 'Ionicons', name: 'help-circle' };

  switch (iconInfo.family) {
    case 'AntDesign':
      return <AntDesign name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'Entypo':
      return <Entypo name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'EvilIcons':
      return <EvilIcons name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'Feather':
      return <Feather name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'FontAwesome':
      return <FontAwesome name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'FontAwesome5':
      return <FontAwesome5 name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'FontAwesome6':
      return <FontAwesome6 name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'Fontisto':
      return <Fontisto name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'Foundation':
      return <Foundation name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'Ionicons':
      return <Ionicons name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'MaterialCommunityIcons':
      return <MaterialCommunityIcons name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'MaterialIcons':
      return <MaterialIcons name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'Octicons':
      return <Octicons name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'SimpleLineIcons':
      return <SimpleLineIcons name={iconInfo.name as any} size={size} color={color} style={style} />;
    case 'Zocial':
      return <Zocial name={iconInfo.name as any} size={size} color={color} style={style} />;
    default:
      return <Ionicons name="help-circle" size={size} color={color} style={style} />;
  }
}

