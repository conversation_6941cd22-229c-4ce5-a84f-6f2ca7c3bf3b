import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef } from 'react';
import { Animated, Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

import ScreenWrapper from '@/components/ScreenWrapper';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

const { width } = Dimensions.get('window');

interface FlowStepProps {
  icon: string;
  title: string;
  description: string;
  color: string;
  isLast?: boolean;
  index: number;
}

const AnimatedFlowStep: React.FC<FlowStepProps> = ({ icon, title, description, color, isLast = false, index }) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  useEffect(() => {
    const delay = index * 200;
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        delay,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        delay,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        delay: delay + 200,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  return (
    <Animated.View
      style={[
        styles.flowStep,
        {
          opacity: fadeAnim,
          transform: [
            { translateY: slideAnim },
            { scale: scaleAnim }
          ]
        }
      ]}
    >
      <View style={styles.stepContent}>
        <View style={[styles.stepIcon, { backgroundColor: `${color}15` }]}>
          <IconSymbol size={24} name={icon as any} color={color} />
        </View>
        <View style={styles.stepText}>
          <ThemedText style={styles.stepTitle}>{title}</ThemedText>
          <ThemedText style={styles.stepDescription}>{description}</ThemedText>
        </View>
        <View style={[styles.stepNumber, { backgroundColor: color }]}>
          <ThemedText style={styles.stepNumberText}>{index + 1}</ThemedText>
        </View>
      </View>
      {!isLast && (
        <View style={styles.stepConnector}>
          <View style={[styles.connectorLine, { backgroundColor: `${color}40` }]} />
          <View style={[styles.connectorDot, { backgroundColor: color }]} />
        </View>
      )}
    </Animated.View>
  );
};

interface UserFlowProps {
  title: string;
  subtitle: string;
  color: string;
  steps: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
}

const EnhancedUserFlow: React.FC<UserFlowProps> = ({ title, subtitle, color, steps }) => {
  const cardAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(cardAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

  return (
    <Animated.View style={[styles.flowCard, { opacity: cardAnim }]}>
      <View style={styles.flowHeader}>
        <LinearGradient
          colors={[color, `${color}CC`, `${color}99`]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.flowHeaderGradient}
        >
          <View style={styles.headerContent}>
            <View style={styles.roleIconContainer}>
              <IconSymbol
                size={32}
                name={title.includes('Honorable') ? 'crown.fill' :
                     title.includes('Manager') ? 'person.badge.key.fill' :
                     'person.crop.circle.badge.checkmark'}
                color="white"
              />
            </View>
            <View style={styles.headerText}>
              <ThemedText style={styles.flowTitle}>{title}</ThemedText>
              <ThemedText style={styles.flowSubtitle}>{subtitle}</ThemedText>
            </View>
          </View>
        </LinearGradient>
      </View>
      <View style={styles.flowSteps}>
        {steps.map((step, index) => (
          <AnimatedFlowStep
            key={index}
            icon={step.icon}
            title={step.title}
            description={step.description}
            color={color}
            isLast={index === steps.length - 1}
            index={index}
          />
        ))}
      </View>
    </Animated.View>
  );
};

export default function UserFlowsScreen() {
  const router = useRouter();
  const { user } = useAuth();
  const headerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(headerAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);

  const getCurrentUserFlow = () => {
    const honFlow = {
      title: "Honorable Member",
      subtitle: "Full system access and oversight",
      color: Colors.light.primary,
      steps: [
        {
          icon: "house.fill",
          title: "Dashboard Overview",
          description: "Monitor campaign statistics, polling stations, and overall progress with comprehensive insights"
        },
        {
          icon: "chart.bar.fill",
          title: "Statistics & Analytics",
          description: "Access detailed campaign analytics, vote distribution, and performance insights across all stations"
        },
        {
          icon: "person.2.fill",
          title: "Agent Management",
          description: "Add and manage both agents and managers across all polling stations with full oversight"
        },
        {
          icon: "gear",
          title: "System Settings",
          description: "Configure system preferences, notifications, security settings, and administrative controls"
        }
      ]
    };

    const managerFlow = {
      title: "Campaign Manager",
      subtitle: "Operational management and oversight",
      color: "#10B981",
      steps: [
        {
          icon: "house.fill",
          title: "Dashboard Overview",
          description: "Monitor campaign progress, active agents, and DR form submissions in real-time"
        },
        {
          icon: "chart.bar.fill",
          title: "Statistics & Reports",
          description: "View campaign analytics, generate performance reports, and track key metrics"
        },
        {
          icon: "person.fill.badge.plus",
          title: "Agent Management",
          description: "Add new polling agents, assign stations, and monitor their field activities"
        },
        {
          icon: "bell.fill",
          title: "Notifications",
          description: "Receive real-time alerts about agent activities, form submissions, and system updates"
        }
      ]
    };

    const agentFlow = {
      title: "Polling Agent",
      subtitle: "Field operations and DR form submission",
      color: "#F59E0B",
      steps: [
        {
          icon: "house.fill",
          title: "Dashboard",
          description: "View your assigned polling station details, current status, and daily objectives"
        },
        {
          icon: "camera.fill",
          title: "DR Form Capture",
          description: "Take high-quality photos of DR forms using the built-in camera interface"
        },
        {
          icon: "arrow.up.doc.fill",
          title: "Form Submission",
          description: "Upload captured DR forms with detailed status descriptions and verification"
        },
        {
          icon: "checkmark.circle.fill",
          title: "Status Updates",
          description: "Update your polling station status, location, and activity throughout the day"
        }
      ]
    };

    switch (user?.role) {
      case 'hon': return honFlow;
      case 'manager': return managerFlow;
      case 'agent': return agentFlow;
      default: return honFlow;
    }
  };

  const currentFlow = getCurrentUserFlow();

  return (
    <ScreenWrapper backgroundColor={Colors.light.neutral[50]}>
      {/* Enhanced Header */}
      <Animated.View style={{ opacity: headerAnim }}>
        <LinearGradient
          colors={[Colors.light.primary, Colors.light.secondary, '#8B5CF6']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.header}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => router.back()}
              activeOpacity={0.8}
            >
              <IconSymbol size={24} name="chevron.left" color="white" />
            </TouchableOpacity>
            <View style={styles.headerTextContainer}>
              <ThemedText style={styles.headerTitle}>Your Workflow</ThemedText>
              <ThemedText style={styles.headerSubtitle}>
                Step-by-step guide for {user?.role === 'hon' ? 'Honorable Members' :
                                       user?.role === 'manager' ? 'Campaign Managers' : 'Polling Agents'}
              </ThemedText>
            </View>
            <View style={styles.headerIcon}>
              <IconSymbol size={28} name="arrow.triangle.branch" color="rgba(255,255,255,0.8)" />
            </View>
          </View>
        </LinearGradient>
      </Animated.View>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Current User Flow */}
        <EnhancedUserFlow {...currentFlow} />

        {/* Enhanced Info Section */}
        <ThemedView style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <IconSymbol size={24} name="lightbulb.fill" color="#F59E0B" />
            <ThemedText style={styles.infoTitle}>Quick Tips</ThemedText>
          </View>
          <ThemedText style={styles.infoText}>
            💡 Follow the steps in order for the best experience{'\n'}
            🔒 Your access level determines available features{'\n'}
            📱 Tap any step to learn more about that function{'\n'}
            🔄 Steps may vary based on your current progress
          </ThemedText>
        </ThemedView>
      </ScrollView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 25,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 26,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
    lineHeight: 18,
  },
  headerIcon: {
    marginLeft: 12,
  },
  flowCard: {
    backgroundColor: 'white',
    borderRadius: 20,
    marginBottom: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 20,
    elevation: 8,
    overflow: 'hidden',
  },
  flowHeader: {
    marginBottom: 0,
  },
  flowHeaderGradient: {
    padding: 24,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  roleIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  headerText: {
    flex: 1,
  },
  flowTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  flowSubtitle: {
    fontSize: 15,
    color: 'rgba(255, 255, 255, 0.95)',
    lineHeight: 20,
  },
  flowSteps: {
    padding: 24,
  },
  flowStep: {
    marginBottom: 20,
  },
  stepContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    position: 'relative',
  },
  stepIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  stepText: {
    flex: 1,
    paddingTop: 6,
    paddingRight: 40,
  },
  stepTitle: {
    fontSize: 17,
    fontWeight: '700',
    color: Colors.light.neutral[800],
    marginBottom: 6,
  },
  stepDescription: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    lineHeight: 22,
  },
  stepNumber: {
    position: 'absolute',
    right: 0,
    top: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepNumberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  stepConnector: {
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
    marginLeft: 28,
  },
  connectorLine: {
    width: 3,
    height: 24,
    borderRadius: 2,
    marginBottom: 8,
  },
  connectorDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 20,
    borderLeftWidth: 5,
    borderLeftColor: '#F59E0B',
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 4,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.neutral[800],
    marginLeft: 12,
  },
  infoText: {
    fontSize: 15,
    color: Colors.light.neutral[600],
    lineHeight: 24,
  },
});
