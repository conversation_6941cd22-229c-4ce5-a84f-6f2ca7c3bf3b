import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import React from 'react';
import { Dimensions, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

import ScreenWrapper from '@/components/ScreenWrapper';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';

const { width } = Dimensions.get('window');

interface FlowStepProps {
  icon: string;
  title: string;
  description: string;
  color: string;
  isLast?: boolean;
}

const FlowStep: React.FC<FlowStepProps> = ({ icon, title, description, color, isLast = false }) => (
  <View style={styles.flowStep}>
    <View style={styles.stepContent}>
      <View style={[styles.stepIcon, { backgroundColor: `${color}15` }]}>
        <IconSymbol size={24} name={icon as any} color={color} />
      </View>
      <View style={styles.stepText}>
        <ThemedText style={styles.stepTitle}>{title}</ThemedText>
        <ThemedText style={styles.stepDescription}>{description}</ThemedText>
      </View>
    </View>
    {!isLast && (
      <View style={styles.stepConnector}>
        <View style={[styles.connectorLine, { backgroundColor: color }]} />
        <IconSymbol size={16} name="chevron.down" color={color} />
      </View>
    )}
  </View>
);

interface UserFlowProps {
  title: string;
  subtitle: string;
  color: string;
  steps: Array<{
    icon: string;
    title: string;
    description: string;
  }>;
}

const UserFlow: React.FC<UserFlowProps> = ({ title, subtitle, color, steps }) => (
  <ThemedView style={styles.flowCard}>
    <View style={styles.flowHeader}>
      <LinearGradient
        colors={[color, `${color}CC`]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.flowHeaderGradient}
      >
        <ThemedText style={styles.flowTitle}>{title}</ThemedText>
        <ThemedText style={styles.flowSubtitle}>{subtitle}</ThemedText>
      </LinearGradient>
    </View>
    <View style={styles.flowSteps}>
      {steps.map((step, index) => (
        <FlowStep
          key={index}
          icon={step.icon}
          title={step.title}
          description={step.description}
          color={color}
          isLast={index === steps.length - 1}
        />
      ))}
    </View>
  </ThemedView>
);

export default function UserFlowsScreen() {
  const router = useRouter();
  const { user } = useAuth();

  const honFlow = {
    title: "Honorable Member",
    subtitle: "Full system access and oversight",
    color: Colors.light.primary,
    steps: [
      {
        icon: "house.fill",
        title: "Dashboard Overview",
        description: "View campaign statistics, polling stations, and DR form collection progress"
      },
      {
        icon: "chart.bar.fill",
        title: "Statistics & Analytics",
        description: "Access detailed campaign analytics, vote distribution, and performance insights"
      },
      {
        icon: "person.2.fill",
        title: "Agent Management",
        description: "Add and manage both agents and managers across all polling stations"
      },
      {
        icon: "gear",
        title: "System Settings",
        description: "Configure system preferences, notifications, and security settings"
      }
    ]
  };

  const managerFlow = {
    title: "Campaign Manager",
    subtitle: "Operational management and oversight",
    color: "#10B981",
    steps: [
      {
        icon: "house.fill",
        title: "Dashboard Overview",
        description: "Monitor campaign progress, active agents, and DR form submissions"
      },
      {
        icon: "chart.bar.fill",
        title: "Statistics & Reports",
        description: "View campaign analytics and generate performance reports"
      },
      {
        icon: "person.fill.badge.plus",
        title: "Agent Management",
        description: "Add new polling agents and monitor their activities"
      },
      {
        icon: "bell.fill",
        title: "Notifications",
        description: "Receive alerts about agent activities and system updates"
      }
    ]
  };

  const agentFlow = {
    title: "Polling Agent",
    subtitle: "Field operations and DR form submission",
    color: "#F59E0B",
    steps: [
      {
        icon: "house.fill",
        title: "Dashboard",
        description: "View your assigned polling station and current status"
      },
      {
        icon: "camera.fill",
        title: "DR Form Capture",
        description: "Take photos of DR forms using the camera interface"
      },
      {
        icon: "arrow.up.doc.fill",
        title: "Form Submission",
        description: "Upload captured DR forms with status descriptions"
      },
      {
        icon: "checkmark.circle.fill",
        title: "Status Updates",
        description: "Update your polling station status and location"
      }
    ]
  };

  return (
    <ScreenWrapper backgroundColor={Colors.light.neutral[50]}>
      {/* Header */}
      <LinearGradient
        colors={[Colors.light.primary, Colors.light.secondary]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <IconSymbol size={24} name="chevron.left" color="white" />
          </TouchableOpacity>
          <View style={styles.headerTextContainer}>
            <ThemedText style={styles.headerTitle}>User Flows</ThemedText>
            <ThemedText style={styles.headerSubtitle}>
              How each user type navigates the app
            </ThemedText>
          </View>
        </View>
      </LinearGradient>

      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Current User Highlight */}
        <ThemedView style={styles.currentUserCard}>
          <View style={styles.currentUserHeader}>
            <IconSymbol size={20} name="person.crop.circle.fill" color={Colors.light.primary} />
            <ThemedText style={styles.currentUserText}>
              You are currently logged in as: <ThemedText style={styles.currentUserRole}>
                {user?.role === 'hon' ? 'Honorable Member' : 
                 user?.role === 'manager' ? 'Campaign Manager' : 'Polling Agent'}
              </ThemedText>
            </ThemedText>
          </View>
        </ThemedView>

        {/* User Flows */}
        <UserFlow {...honFlow} />
        <UserFlow {...managerFlow} />
        <UserFlow {...agentFlow} />

        {/* Info Section */}
        <ThemedView style={styles.infoCard}>
          <View style={styles.infoHeader}>
            <IconSymbol size={20} name="info.circle.fill" color={Colors.light.info} />
            <ThemedText style={styles.infoTitle}>Access Control</ThemedText>
          </View>
          <ThemedText style={styles.infoText}>
            • Each user type has specific permissions and access levels{'\n'}
            • Agents can only access their dashboard and DR form functions{'\n'}
            • Managers can view statistics and manage agents{'\n'}
            • Honorable Members have full system access and oversight
          </ThemedText>
        </ThemedView>
      </ScrollView>
    </ScreenWrapper>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 40,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  headerTextContainer: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  currentUserCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  currentUserHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentUserText: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    marginLeft: 12,
    flex: 1,
  },
  currentUserRole: {
    fontWeight: '600',
    color: Colors.light.primary,
  },
  flowCard: {
    backgroundColor: 'white',
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
    overflow: 'hidden',
  },
  flowHeader: {
    marginBottom: 0,
  },
  flowHeaderGradient: {
    padding: 20,
  },
  flowTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  flowSubtitle: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.9)',
  },
  flowSteps: {
    padding: 20,
  },
  flowStep: {
    marginBottom: 16,
  },
  stepContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  stepIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  stepText: {
    flex: 1,
    paddingTop: 4,
  },
  stepTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginBottom: 4,
  },
  stepDescription: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    lineHeight: 20,
  },
  stepConnector: {
    alignItems: 'center',
    marginTop: 12,
    marginBottom: 4,
  },
  connectorLine: {
    width: 2,
    height: 20,
    marginBottom: 4,
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.info,
    shadowColor: Colors.light.neutral[900],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.neutral[800],
    marginLeft: 8,
  },
  infoText: {
    fontSize: 14,
    color: Colors.light.neutral[600],
    lineHeight: 20,
  },
});
