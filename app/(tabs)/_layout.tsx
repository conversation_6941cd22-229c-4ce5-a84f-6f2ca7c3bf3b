import { Tabs, useRouter } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useAuth } from '@/context/AuthContext';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const { user } = useAuth();
  const router = useRouter();

  // Determine if user is Hon/admin or manager (they see the same tabs)
  const isHon = user?.role === 'hon';
  const isManager = user?.role === 'manager';
  const isAgent = user?.role === 'agent';

  // No need for redirect here - AuthGuard handles this

  // Render different tab structures based on user role
  if (isAgent) {
    // Agent sees only Dashboard
    return (
      <Tabs
        screenOptions={{
          tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
          headerShown: false,
          tabBarButton: HapticTab,
          tabBarBackground: TabBarBackground,
          unmountOnBlur: false,
          freezeOnBlur: true,
          tabBarStyle: Platform.select({
            ios: {
              position: 'absolute',
            },
            default: {},
          }),
        }}>
        {/* Dashboard - only tab for agents */}
        <Tabs.Screen
          name="index"
          options={{
            title: 'Dashboard',
            tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
          }}
        />

        {/* Hidden screens - accessible via navigation but not in tab bar */}
        <Tabs.Screen
          name="profile"
          options={{
            href: null,
          }}
        />
        <Tabs.Screen
          name="upload-dr"
          options={{
            href: null,
          }}
        />
        <Tabs.Screen
          name="statistics"
          options={{
            href: null,
          }}
        />
        <Tabs.Screen
          name="agents"
          options={{
            href: null,
          }}
        />
        <Tabs.Screen
          name="add-agent"
          options={{
            href: null,
          }}
        />
        <Tabs.Screen
          name="user-flows"
          options={{
            href: null,
          }}
        />
      </Tabs>
    );
  }

  // Hon/Manager sees all tabs
  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        unmountOnBlur: false,
        freezeOnBlur: true,
        tabBarStyle: Platform.select({
          ios: {
            position: 'absolute',
          },
          default: {},
        }),
      }}>
      {/* Dashboard - visible to all users */}
      <Tabs.Screen
        name="index"
        options={{
          title: 'Dashboard',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="house.fill" color={color} />,
        }}
      />

      {/* Statistics - visible to Hon/admin and managers */}
      <Tabs.Screen
        name="statistics"
        options={{
          title: 'Statistics',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="chart.bar.fill" color={color} />,
        }}
      />

      {/* Agents - visible to Hon/admin and managers */}
      <Tabs.Screen
        name="agents"
        options={{
          title: 'Agents',
          tabBarIcon: ({ color }) => <IconSymbol size={28} name="person.2.fill" color={color} />,
        }}
      />

      {/* Hidden screens - accessible via navigation but not in tab bar */}
      <Tabs.Screen
        name="profile"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="add-agent"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="upload-dr"
        options={{
          href: null,
        }}
      />
      <Tabs.Screen
        name="user-flows"
        options={{
          href: null,
        }}
      />
    </Tabs>
  );
}
